<template>
	<view class="webview-container">
		<web-view :src="webviewUrl" @message="handleMessage" @error="handleError"></web-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			webviewUrl: '',
			title: '预览'
		}
	},
	onLoad(options) {
		if (options.url) {
			this.webviewUrl = decodeURIComponent(options.url);
		}
		if (options.title) {
			this.title = decodeURIComponent(options.title);
			uni.setNavigationBarTitle({
				title: this.title
			});
		}
	},
	methods: {
		handleMessage(event) {
			console.log('webview message:', event);
		},
		handleError(event) {
			console.log('webview error:', event);
			uni.showToast({
				icon: 'none',
				title: '加载失败，请重试'
			});
		}
	}
}
</script>

<style scoped>
.webview-container {
	width: 100%;
	height: 100vh;
}
</style>
